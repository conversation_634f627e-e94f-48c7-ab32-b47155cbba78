# Stock Trading Application

A comprehensive stock trading and portfolio analytics platform featuring real-time market data, machine learning-powered price predictions, and advanced portfolio management capabilities.

## 🚀 Features

### Core Functionality
- **Real-time Portfolio Tracking**: Monitor portfolio performance with live market data
- **ML Price Predictions**: Advanced transformer-based models for stock price forecasting
- **Interactive Analytics**: Comprehensive charts and visualizations using D3.js and ECharts
- **Multi-Account Management**: Support for multiple trading accounts with detailed analytics
- **Automated Trading Strategies**: Built-in strategy analysis and recommendations
- **Market Data Integration**: Real-time data from Yahoo Finance and other sources

### Technical Features
- **Time-Weighted Returns**: Accurate performance calculations accounting for cash flows
- **Advanced Technical Indicators**: RSI, MACD, Moving Averages, Stochastic Oscillator
- **Model Training Pipeline**: Automated daily model retraining with hyperparameter optimization
- **Caching System**: Intelligent data caching for improved performance
- **Responsive Design**: Modern Material-UI interface optimized for all devices

## 🏗️ Architecture

### Backend (Flask)
```
backend/
├── src/
│   ├── api/                    # REST API endpoints
│   │   ├── accounts.py         # Account management
│   │   ├── analytics.py        # Portfolio analytics
│   │   ├── market.py          # Market data endpoints
│   │   ├── strategies.py      # Trading strategies & predictions
│   │   └── transactions.py    # Transaction management
│   ├── core/
│   │   └── scheduler.py       # Background job scheduler
│   ├── models/
│   │   └── prediction_model/  # ML prediction pipeline
│   │       ├── model.py       # Transformer model implementation
│   │       └── prediction_pipeline.py  # Prediction orchestration
│   ├── services/              # Business logic services
│   ├── utils/                 # Utility functions and helpers
│   └── app.py                # Main Flask application
├── data/
│   ├── models/prediction_model_data/  # Trained ML models
│   ├── cache/                 # Data cache storage
│   └── stock_trading.db      # SQLite database
├── config/
│   ├── schema.sql            # Database schema
│   └── clear_data.sql        # Data cleanup scripts
└── tests/                    # Comprehensive test suite
```

### Frontend (React + Vite)
```
frontend/
├── src/
│   ├── components/           # Reusable React components
│   ├── pages/               # Page-level components
│   ├── api/                 # API client utilities
│   └── utils/               # Frontend utilities
├── package.json
└── vite.config.js          # Vite configuration
```

### Database (SQLite)
- **Accounts**: User account management
- **Transactions**: Trade history and portfolio changes
- **Cache Tables**: Market data and performance caching
- **Optimized Indexes**: For fast query performance

## 📋 Prerequisites

### System Requirements
- **Python 3.8+** (3.10+ recommended)
- **Node.js 16+** (18+ recommended)
- **npm 8+** or **yarn 1.22+**
- **SQLite 3** (usually included with Python)
- **Git** for version control

### Platform Support
- ✅ macOS (Intel/Apple Silicon)
- ✅ Linux (Ubuntu 20.04+, CentOS 8+)
- ✅ Windows 10/11

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone https://github.com/MortyMinds/stock.git
cd stock
```

### 2. Backend Setup

#### Create Virtual Environment
```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
venv\Scripts\activate
```

#### Install Python Dependencies
```bash
pip install -r requirements.txt
```

#### Initialize Database
```bash
# Create data directory
mkdir -p backend/data

# Initialize database with schema
sqlite3 backend/data/stock_trading.db < backend/config/schema.sql
```

### 3. Frontend Setup

#### Install Node.js Dependencies
```bash
cd frontend
npm install
cd ..
```

### 4. Environment Configuration (Optional)

Create a `.env` file in the root directory for custom configuration:
```bash
# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
CORS_MAX_AGE=600

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=1
```

## 🚀 Running the Application

### Quick Start (Recommended)

Use the provided startup scripts to run both servers simultaneously:

#### On macOS/Linux:
```bash
./start-dev.sh
```

#### On Windows:
```bash
start-dev.bat
```

These scripts will:
- ✅ Check all prerequisites
- ✅ Set up virtual environment if needed
- ✅ Install missing dependencies
- ✅ Initialize database if needed
- ✅ Start both backend and frontend servers
- ✅ Provide clear status updates and error handling

### Manual Startup

If you prefer to start servers manually:

#### Backend Server (Port 5001)
```bash
cd backend
source ../venv/bin/activate  # On Windows: ..\venv\Scripts\activate
python -m src.app
```

#### Frontend Server (Port 5173)
```bash
cd frontend
npm run dev
```

### Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5001
- **API Documentation**: http://localhost:5001/api (when available)

## 📡 API Documentation

### Core Endpoints

#### Portfolio Analytics
- **GET** `/api/analytics/overview` - Portfolio performance overview
- **GET** `/api/analytics/performance` - Detailed performance metrics
- **GET** `/api/analytics/time-series` - Historical performance data

#### Account Management
- **GET** `/api/accounts` - List all accounts
- **POST** `/api/accounts` - Create new account
- **GET** `/api/accounts/{id}` - Get account details

#### Transaction Management
- **GET** `/api/transactions` - List transactions
- **POST** `/api/transactions` - Record new transaction
- **PUT** `/api/transactions/{id}` - Update transaction

#### Market Data
- **GET** `/api/market/hotspots` - Top gainers/losers
- **GET** `/api/market/fear-greed-index` - Market sentiment indicator
- **GET** `/api/stocks/prices/update` - Update cached stock prices

#### Trading Strategies & Predictions
- **GET** `/api/strategies/stocks` - Available stocks for analysis
- **GET** `/api/strategies/analysis` - Strategy recommendations
- **GET** `/api/strategies/get_model_future_predictions` - ML price predictions

### Response Format
All API responses follow a consistent JSON format:
```json
{
  "data": { ... },
  "status": "success|error",
  "message": "Optional message",
  "timestamp": "2025-07-03T19:10:13Z"
}
```

## 🧪 Testing

### Backend Testing
The backend includes a comprehensive test suite with 80%+ coverage:

```bash
# Run all backend tests
cd backend
python -m pytest

# Run with coverage report
python -m pytest --cov=src --cov-report=html

# Run specific test categories
python -m pytest tests/unit/          # Unit tests
python -m pytest tests/integration/   # Integration tests
```

### Frontend Testing
```bash
cd frontend

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in UI mode
npm run test:ui
```

### Test Database
Tests use isolated test databases to avoid affecting development data:
- Backend tests: `test_stock_trading.db`
- Automatic cleanup after each test run

## 🔧 Development

### Development Features
- **Hot Module Replacement (HMR)**: Frontend changes reflect instantly
- **Debug Mode**: Backend runs with detailed error reporting
- **Auto-reload**: Backend restarts automatically on code changes
- **Source Maps**: Full debugging support for both frontend and backend

### Code Quality
- **ESLint**: Frontend code linting
- **Pytest**: Backend testing framework
- **Type Hints**: Python type annotations for better code quality
- **Material-UI**: Consistent design system

### Database Management
```bash
# Reset database (⚠️ This will delete all data)
sqlite3 backend/data/stock_trading.db < backend/config/clear_data.sql
sqlite3 backend/data/stock_trading.db < backend/config/schema.sql

# Backup database
cp backend/data/stock_trading.db backend/data/stock_trading_backup.db
```

## 🔄 Recent Updates

### Model Checkpoint Path Fix (July 2025)
**Issue Resolved**: Fixed critical path mismatch in ML prediction pipeline
- **Problem**: Prediction pipeline couldn't find pre-trained models due to incorrect path configuration
- **Solution**: Updated `MODEL_DIR_NAME` in `prediction_pipeline.py` to match actual model storage location
- **Impact**: On-demand predictions now work correctly without "Model checkpoint not found" warnings
- **Files Modified**: `backend/src/models/prediction_model/prediction_pipeline.py`

### Key Technical Details
- Models are stored in: `backend/data/models/prediction_model_data/`
- Each ticker has its own subdirectory with `.pth` and `.json` files
- Daily training job continues to save models to correct location
- Prediction API endpoint `/api/strategies/get_model_future_predictions` now fully functional

## 🚀 Production Deployment

### Frontend Build
```bash
cd frontend
npm run build
```

### Backend Production Server
```bash
# Using Gunicorn (recommended)
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5001 backend.src.app:app

# Using uWSGI
pip install uwsgi
uwsgi --http :5001 --module backend.src.app:app --processes 4
```

### Environment Variables for Production
```bash
export FLASK_ENV=production
export FLASK_DEBUG=0
export ALLOWED_ORIGINS=https://yourdomain.com
```

## 🐛 Troubleshooting

### Common Issues

#### "Model checkpoint not found" Warning
**Status**: ✅ **RESOLVED** (July 2025)
- This issue has been fixed with the recent model path update
- If you still see this warning, ensure you're using the latest code

#### Port Already in Use
```bash
# Kill processes on specific ports
lsof -ti:5001 | xargs kill -9  # Backend
lsof -ti:5173 | xargs kill -9  # Frontend
```

#### Virtual Environment Issues
```bash
# Recreate virtual environment
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### Database Corruption
```bash
# Backup and recreate database
cp backend/data/stock_trading.db backend/data/backup.db
rm backend/data/stock_trading.db
sqlite3 backend/data/stock_trading.db < backend/config/schema.sql
```

#### Frontend Build Errors
```bash
# Clear npm cache and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### Getting Help
- Check the [Issues](https://github.com/MortyMinds/stock/issues) page for known problems
- Review logs in `backend/logs/` for detailed error information
- Ensure all prerequisites are installed and up to date

## 🤝 Contributing

### Development Workflow
1. **Fork** the repository
2. **Clone** your fork locally
3. **Create** a feature branch: `git checkout -b feature/amazing-feature`
4. **Make** your changes with proper tests
5. **Test** thoroughly: Run both backend and frontend test suites
6. **Commit** with clear messages: `git commit -m 'Add amazing feature'`
7. **Push** to your branch: `git push origin feature/amazing-feature`
8. **Create** a Pull Request with detailed description

### Code Standards
- **Python**: Follow PEP 8, include type hints, write comprehensive tests
- **JavaScript/React**: Follow ESLint rules, write component tests
- **Documentation**: Update README and inline comments for significant changes
- **Testing**: Maintain 80%+ test coverage for new code

### Pull Request Guidelines
- Include clear description of changes and motivation
- Reference any related issues
- Ensure all tests pass
- Update documentation as needed
- Add screenshots for UI changes

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details

## 🙏 Acknowledgments

- **Yahoo Finance** for market data API
- **PyTorch** for machine learning framework
- **React** and **Material-UI** for frontend framework
- **Flask** for backend API framework
