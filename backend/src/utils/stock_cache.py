import time
import yfinance as yf
import pandas as pd
from pathlib import Path
import json
import os
from .logger import get_logger, log_exceptions

@log_exceptions
class StockCache:
    def __init__(self, cache_dir: str = None, cache_expiry: int = 24*60*60):
        # 使用绝对路径 - updated for new structure
        if cache_dir is None:
            cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data', 'cache')
        self.cache_dir = Path(cache_dir)
        self.cache_expiry = cache_expiry
        self.cache_dir.mkdir(exist_ok=True)
        self.logger = get_logger()

    def get_historical_data(self, symbol: str) -> pd.DataFrame:
        """Get historical price data with local caching"""
        # Clean the symbol
        symbol = symbol.strip().replace('"', '').upper()
        cache_file = self.cache_dir / f"{symbol}_data.parquet"

        # Try to read from cache first
        if cache_file.exists():
            cache_time = cache_file.stat().st_mtime
            if time.time() - cache_time < self.cache_expiry:
                try:
                    data = pd.read_parquet(cache_file)
                    if isinstance(data.columns, pd.MultiIndex):
                        data.columns = data.columns.get_level_values('Price')
                    if not data.empty:
                        return data
                except Exception as e:
                    self.logger.error(f"Cache read error for {symbol}: {e}")

        # Download fresh data
        try:
            data = yf.download(symbol, period="1y", interval="1h", progress=False)
        except Exception as e:
            self.logger.exception(f"Error downloading data for {symbol}: {e}")
            return pd.DataFrame()

        # Check if data is valid
        if data.empty:
            self.logger.error(f"No data available for {symbol}")
            return pd.DataFrame()

        # Process the data
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = data.columns.get_level_values('Price')

        # Try to cache the data
        try:
            data.to_parquet(cache_file)
        except Exception as e:
            self.logger.error(f"Cache write error for {symbol}: {e}")

        return data 