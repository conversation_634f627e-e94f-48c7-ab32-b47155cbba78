"""
Options Trading Strategies Implementation

Migrated and integrated from the standalone options analysis system.
Provides all strategy analysis capabilities:
- Cash-Secured Puts (Wheel Strategy entry)
- Covered Calls (Wheel Strategy exit)  
- Iron Condors (Neutral strategy)
- Win Rate Scoring algorithms
- Risk assessment and position sizing
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from scipy.stats import norm
import math

from src.utils.logger import get_logger


class OptionsStrategiesAnalyzer:
    """Core options strategies analysis engine."""
    
    def __init__(self):
        self.logger = get_logger()
    
    def find_cash_secured_put_candidates(self, df: pd.DataFrame, prices: Dict[str, float], 
                                       params: Dict[str, Any]) -> pd.DataFrame:
        """
        Finds and filters cash-secured put candidates based on strategy parameters.
        Migrated from wheel_strategy.py with full functionality preserved.
        """
        if df.empty:
            self.logger.warning("No option chain data to analyze for cash-secured puts.")
            return pd.DataFrame()
        
        # 1. Initial Filtering for Puts
        candidates = df[df['optionType'] == 'puts'].copy()
        if candidates.empty:
            self.logger.warning("No put options found in the retrieved data.")
            return pd.DataFrame()
        
        # Add current prices
        candidates['currentPrice'] = candidates['symbol'].map(prices)
        candidates = candidates.dropna(subset=['currentPrice'])
        
        if candidates.empty:
            self.logger.warning("No current price data available for put options.")
            return pd.DataFrame()
        
        # 2. DTE Filtering
        min_dte = params.get('min_dte', 20)
        max_dte = params.get('max_dte', 60)
        candidates = candidates[(candidates['dte'] >= min_dte) & (candidates['dte'] <= max_dte)]
        
        if candidates.empty:
            self.logger.warning(f"No put candidates found within DTE range {min_dte}-{max_dte} days.")
            return pd.DataFrame()
        
        # 3. Strike Price Filtering (Out-of-the-money puts)
        candidates = candidates[candidates['strike'] < candidates['currentPrice']]
        
        if candidates.empty:
            self.logger.warning("No out-of-the-money put candidates found.")
            return pd.DataFrame()
        
        # 4. Calculate Key Metrics
        candidates['premium'] = candidates['bid'] * 100  # Premium per contract
        candidates['maxGain'] = candidates['premium']
        candidates['maxLoss'] = (candidates['strike'] * 100) - candidates['premium']
        candidates['bufferPercent'] = (candidates['currentPrice'] - candidates['strike']) / candidates['currentPrice']
        
        # Buffer percentage filtering
        min_buffer = params.get('min_buffer_percent', 0.05)
        candidates = candidates[candidates['bufferPercent'] >= min_buffer]
        
        if candidates.empty:
            self.logger.warning(f"No put candidates found with buffer >= {min_buffer:.1%}.")
            return pd.DataFrame()
        
        # 5. ROI Calculations
        candidates['annualizedRoi'] = (candidates['bid'] / candidates['strike']) * (365 / candidates['dte'])
        
        # ROI filtering
        min_annual_roi = params.get('min_annual_roi', 0.15)
        candidates = candidates[candidates['annualizedRoi'] >= min_annual_roi]
        
        if candidates.empty:
            self.logger.warning(f"No put candidates found with annualized ROI >= {min_annual_roi:.1%}.")
            return pd.DataFrame()
        
        # 6. Risk/Reward Metrics
        candidates['riskRewardRatio'] = candidates['premium'] / candidates['maxLoss']
        candidates['returnOnRisk'] = (candidates['premium'] / (candidates['strike'] * 100)) * 100
        
        # 7. Calculate Win Rate Score
        candidates['winRateScore'] = self._calculate_put_win_rate_score(candidates)
        
        # 8. DTE Category for scoring
        candidates['dte_category'] = candidates['dte'].apply(self._categorize_dte)
        candidates['dte_score'] = candidates['dte_category'].map({
            'Optimal': 20, 'Short': 10, 'Extended': 15, 'Long': 5
        })
        
        # 9. Composite Score
        candidates['composite_score'] = (
            candidates['winRateScore'] * 0.7 +
            candidates['dte_score'] * 0.3
        )
        
        # 10. Select Best Candidates
        sort_by = params.get('sort_by', 'winRateScore')
        max_candidates = params.get('max_candidates', 10)
        
        if sort_by not in candidates.columns:
            sort_by = 'composite_score'
        
        best_candidates = candidates.loc[candidates.groupby('symbol')[sort_by].idxmax()]
        
        # 11. Format Results
        result_cols = [
            'symbol', 'optionType', 'currentPrice', 'strike', 'expiration', 'dte', 'bid', 'premium',
            'maxGain', 'maxLoss', 'annualizedRoi', 'bufferPercent', 'winRateScore',
            'impliedVolatility', 'riskRewardRatio', 'returnOnRisk'
        ]
        
        final_results = best_candidates[result_cols].sort_values(by=sort_by, ascending=False)
        return final_results.head(max_candidates)
    
    def find_covered_call_candidates(self, df: pd.DataFrame, prices: Dict[str, float], 
                                   params: Dict[str, Any]) -> pd.DataFrame:
        """
        Finds and filters covered call candidates based on strategy parameters.
        Migrated from wheel_strategy.py with full functionality preserved.
        """
        if df.empty:
            self.logger.warning("No option chain data to analyze for covered calls.")
            return pd.DataFrame()
        
        # 1. Initial Filtering for Calls
        candidates = df[df['optionType'] == 'calls'].copy()
        if candidates.empty:
            self.logger.warning("No call options found in the retrieved data.")
            return pd.DataFrame()
        
        # Add current prices
        candidates['currentPrice'] = candidates['symbol'].map(prices)
        candidates = candidates.dropna(subset=['currentPrice'])
        
        if candidates.empty:
            self.logger.warning("No current price data available for call options.")
            return pd.DataFrame()
        
        # 2. DTE Filtering
        min_dte = params.get('min_dte', 20)
        max_dte = params.get('max_dte', 60)
        candidates = candidates[(candidates['dte'] >= min_dte) & (candidates['dte'] <= max_dte)]
        
        if candidates.empty:
            self.logger.warning(f"No call candidates found within DTE range {min_dte}-{max_dte} days.")
            return pd.DataFrame()
        
        # 3. Strike Price Filtering (At or out-of-the-money calls)
        candidates = candidates[candidates['strike'] >= candidates['currentPrice']]
        
        if candidates.empty:
            self.logger.warning("No at-the-money or out-of-the-money call candidates found.")
            return pd.DataFrame()
        
        # 4. Calculate Key Metrics
        candidates['premium'] = candidates['bid'] * 100  # Premium per contract
        candidates['maxGain'] = candidates['premium']
        candidates['upsideBufferPercent'] = (candidates['strike'] - candidates['currentPrice']) / candidates['currentPrice']
        
        # Upside buffer filtering
        min_upside_buffer = params.get('min_upside_buffer', 0.02)
        candidates = candidates[candidates['upsideBufferPercent'] >= min_upside_buffer]
        
        if candidates.empty:
            self.logger.warning(f"No call candidates found with upside buffer >= {min_upside_buffer:.1%}.")
            return pd.DataFrame()
        
        # 5. ROI Calculations
        candidates['annualizedRoi'] = (candidates['bid'] / candidates['currentPrice']) * (365 / candidates['dte'])
        
        # ROI filtering
        min_annual_roi = params.get('min_annual_roi', 0.10)
        candidates = candidates[candidates['annualizedRoi'] >= min_annual_roi]
        
        if candidates.empty:
            self.logger.warning(f"No call candidates found with annualized ROI >= {min_annual_roi:.1%}.")
            return pd.DataFrame()
        
        # 6. Additional Metrics
        candidates['totalReturnIfCalled'] = candidates['upsideBufferPercent'] + (candidates['bid'] / candidates['currentPrice'])
        candidates['returnOnInvestment'] = (candidates['bid'] / candidates['currentPrice']) * 100
        candidates['opportunityCost'] = candidates['upsideBufferPercent'] * 100  # Potential gains given up
        
        # 7. Calculate Win Rate Score
        candidates['winRateScore'] = self._calculate_call_win_rate_score(candidates)
        
        # 8. Select Best Candidates
        sort_by = params.get('sort_by', 'annualizedRoi')
        max_candidates = params.get('max_candidates', 10)
        
        if sort_by not in candidates.columns:
            sort_by = 'annualizedRoi'
        
        best_candidates = candidates.loc[candidates.groupby('symbol')[sort_by].idxmax()]
        
        # 9. Format Results
        result_cols = [
            'symbol', 'optionType', 'currentPrice', 'strike', 'expiration', 'dte', 'bid', 'premium',
            'maxGain', 'annualizedRoi', 'upsideBufferPercent', 'totalReturnIfCalled',
            'winRateScore', 'impliedVolatility', 'returnOnInvestment', 'opportunityCost'
        ]
        
        final_results = best_candidates[result_cols].sort_values(by=sort_by, ascending=False)
        return final_results.head(max_candidates)
    
    def _calculate_put_win_rate_score(self, df: pd.DataFrame) -> pd.Series:
        """
        Enhanced win rate score calculation for cash-secured puts.
        Migrated from wheel_strategy.py with market-adaptive weightings.
        """
        # 1. Enhanced Buffer Score with non-linear scaling
        buffer_score = (1 - np.exp(-df['bufferPercent'] / 10)) * 100
        
        # 2. Adaptive Time Score
        optimal_dte = 37.5
        avg_iv = df['impliedVolatility'].mean()
        if avg_iv > 0.5:  # High volatility environment
            optimal_dte = 30
        elif avg_iv < 0.25:  # Low volatility environment
            optimal_dte = 45
        
        time_score = 100 - (abs(df['dte'] - optimal_dte) / optimal_dte * 100).clip(0, 100)
        
        # 3. IV Score - prefer moderate IV levels
        iv_score = np.where(
            df['impliedVolatility'] < 0.2, 
            df['impliedVolatility'] / 0.2 * 50,  # Low IV penalty
            np.where(
                df['impliedVolatility'] <= 0.6,
                50 + (df['impliedVolatility'] - 0.2) / 0.4 * 50,  # Sweet spot
                100 - (df['impliedVolatility'] - 0.6) / 0.4 * 30   # High IV penalty
            )
        )
        
        # 4. Liquidity Score (based on bid-ask spread)
        spread = (df['ask'] - df['bid']) / ((df['ask'] + df['bid']) / 2)
        liquidity_score = np.maximum(0, 100 - spread * 1000)  # Penalize wide spreads
        
        # 5. Momentum Score (simplified - would need price history for full implementation)
        momentum_score = 50  # Neutral score as placeholder
        
        # Dynamic weightings based on market conditions
        if avg_iv > 0.5:  # High volatility - prioritize safety
            weights = {'buffer': 0.45, 'time': 0.20, 'iv': 0.15, 'liquidity': 0.15, 'momentum': 0.05}
        elif avg_iv < 0.25:  # Low volatility - can take more risk for premium
            weights = {'buffer': 0.30, 'time': 0.25, 'iv': 0.25, 'liquidity': 0.15, 'momentum': 0.05}
        else:  # Normal volatility
            weights = {'buffer': 0.35, 'time': 0.25, 'iv': 0.20, 'liquidity': 0.15, 'momentum': 0.05}
        
        # Weighted composite score
        win_rate_score = (
            buffer_score * weights['buffer'] +
            time_score * weights['time'] +
            iv_score * weights['iv'] +
            liquidity_score * weights['liquidity'] +
            momentum_score * weights['momentum']
        )
        
        return win_rate_score.round(1)
    
    def _calculate_call_win_rate_score(self, df: pd.DataFrame) -> pd.Series:
        """
        Enhanced win rate score calculation for covered calls.
        Migrated from wheel_strategy.py.
        """
        # 1. Upside Buffer Score
        upside_buffer_score = np.minimum(100, df['upsideBufferPercent'] * 1000)  # Cap at 100
        
        # 2. Time Score
        optimal_dte = 37.5
        time_score = 100 - (abs(df['dte'] - optimal_dte) / optimal_dte * 100).clip(0, 100)
        
        # 3. IV Score
        iv_score = np.where(
            df['impliedVolatility'] < 0.2, 
            df['impliedVolatility'] / 0.2 * 50,
            np.where(
                df['impliedVolatility'] <= 0.6,
                50 + (df['impliedVolatility'] - 0.2) / 0.4 * 50,
                100 - (df['impliedVolatility'] - 0.6) / 0.4 * 30
            )
        )
        
        # 4. Liquidity Score
        spread = (df['ask'] - df['bid']) / ((df['ask'] + df['bid']) / 2)
        liquidity_score = np.maximum(0, 100 - spread * 1000)
        
        # Weighted composite score
        win_rate_score = (
            upside_buffer_score * 0.35 +
            time_score * 0.25 +
            iv_score * 0.25 +
            liquidity_score * 0.15
        )
        
        return win_rate_score.round(1)
    
    def find_iron_condor_candidates(self, df: pd.DataFrame, prices: Dict[str, float],
                                  params: Dict[str, Any]) -> pd.DataFrame:
        """
        Finds and analyzes iron condor candidates.
        Migrated from iron_condor.py with full functionality preserved.
        """
        if df.empty:
            self.logger.warning("No option chain data to analyze for iron condors.")
            return pd.DataFrame()

        # Separate puts and calls
        puts_df = df[df['optionType'] == 'puts'].copy()
        calls_df = df[df['optionType'] == 'calls'].copy()

        if puts_df.empty or calls_df.empty:
            self.logger.warning("Need both puts and calls data for iron condor analysis.")
            return pd.DataFrame()

        # Add current prices
        puts_df['currentPrice'] = puts_df['symbol'].map(prices)
        calls_df['currentPrice'] = calls_df['symbol'].map(prices)

        # Filter by DTE
        min_dte = params.get('min_dte', 30)
        max_dte = params.get('max_dte', 60)
        puts_df = puts_df[(puts_df['dte'] >= min_dte) & (puts_df['dte'] <= max_dte)]
        calls_df = calls_df[(calls_df['dte'] >= min_dte) & (calls_df['dte'] <= max_dte)]

        # Estimate deltas
        puts_df['delta'] = self._estimate_put_delta(puts_df)
        calls_df['delta'] = self._estimate_call_delta(calls_df)

        iron_condors = []
        target_delta_short = params.get('target_delta_short_put', 0.20)
        min_wing_width = params.get('min_wing_width', 5)
        max_wing_width = params.get('max_wing_width', 20)
        min_annual_roi = params.get('min_annual_roi', 0.20)

        # Group by symbol and expiration
        for symbol in puts_df['symbol'].unique():
            symbol_puts = puts_df[puts_df['symbol'] == symbol]
            symbol_calls = calls_df[calls_df['symbol'] == symbol]
            current_price = prices.get(symbol)

            if not current_price:
                continue

            for exp_date in symbol_puts['expiration'].unique():
                exp_puts = symbol_puts[symbol_puts['expiration'] == exp_date]
                exp_calls = symbol_calls[symbol_calls['expiration'] == exp_date]

                if exp_puts.empty or exp_calls.empty:
                    continue

                dte = exp_puts['dte'].iloc[0]

                # Find short put (target delta around 0.20)
                short_put_candidates = exp_puts[
                    (abs(exp_puts['delta'] + target_delta_short) < 0.05) &
                    (exp_puts['strike'] < current_price)
                ]

                # Find short call (target delta around 0.20)
                short_call_candidates = exp_calls[
                    (abs(exp_calls['delta'] - target_delta_short) < 0.05) &
                    (exp_calls['strike'] > current_price)
                ]

                for _, short_put in short_put_candidates.iterrows():
                    for _, short_call in short_call_candidates.iterrows():
                        short_put_strike = short_put['strike']
                        short_call_strike = short_call['strike']

                        # Find long puts (protective)
                        long_put_candidates = exp_puts[
                            (exp_puts['strike'] < short_put_strike) &
                            (exp_puts['strike'] >= short_put_strike - max_wing_width) &
                            (exp_puts['strike'] <= short_put_strike - min_wing_width)
                        ]

                        # Find long calls (protective)
                        long_call_candidates = exp_calls[
                            (exp_calls['strike'] > short_call_strike) &
                            (exp_calls['strike'] <= short_call_strike + max_wing_width) &
                            (exp_calls['strike'] >= short_call_strike + min_wing_width)
                        ]

                        for _, long_put in long_put_candidates.iterrows():
                            for _, long_call in long_call_candidates.iterrows():
                                long_put_strike = long_put['strike']
                                long_call_strike = long_call['strike']

                                # Calculate iron condor metrics
                                put_width = short_put_strike - long_put_strike
                                call_width = long_call_strike - short_call_strike

                                # Net premium collected
                                net_premium = (
                                    short_put['bid'] + short_call['bid'] -
                                    long_put['ask'] - long_call['ask']
                                ) * 100

                                if net_premium <= 0:
                                    continue

                                # Risk and reward calculations
                                max_profit = net_premium
                                max_loss = max(put_width, call_width) * 100 - net_premium

                                if max_loss <= 0:
                                    continue

                                # Break-even points
                                lower_breakeven = short_put_strike - (net_premium / 100)
                                upper_breakeven = short_call_strike + (net_premium / 100)

                                # Probability of profit (simplified)
                                prob_of_profit = self._calculate_iron_condor_probability(
                                    current_price, lower_breakeven, upper_breakeven, dte
                                )

                                # Annualized ROI
                                annualized_roi = (max_profit / max_loss) * (365 / dte)

                                if annualized_roi < min_annual_roi:
                                    continue

                                iron_condor = {
                                    'symbol': symbol,
                                    'currentPrice': current_price,
                                    'expiration': exp_date,
                                    'dte': dte,
                                    'longPutStrike': long_put_strike,
                                    'shortPutStrike': short_put_strike,
                                    'shortCallStrike': short_call_strike,
                                    'longCallStrike': long_call_strike,
                                    'putWidth': put_width,
                                    'callWidth': call_width,
                                    'netPremium': net_premium,
                                    'maxProfit': max_profit,
                                    'maxLoss': max_loss,
                                    'annualizedRoi': annualized_roi,
                                    'lowerBreakeven': lower_breakeven,
                                    'upperBreakeven': upper_breakeven,
                                    'probabilityOfProfit': prob_of_profit,
                                    'riskRewardRatio': max_loss / max_profit,
                                    'returnOnRisk': (max_profit / max_loss) * 100,
                                }

                                iron_condors.append(iron_condor)

        if not iron_condors:
            self.logger.warning("No viable iron condor candidates found.")
            return pd.DataFrame()

        # Convert to DataFrame and calculate win rate scores
        candidates = pd.DataFrame(iron_condors)
        candidates['winRateScore'] = self._calculate_iron_condor_win_rate_score(candidates)

        # Sort and select best candidates
        sort_by = params.get('sort_by', 'winRateScore')
        max_candidates = params.get('max_candidates', 5)

        if sort_by not in candidates.columns:
            sort_by = 'annualizedRoi'

        best_candidates = candidates.loc[candidates.groupby('symbol')[sort_by].idxmax()]

        # Format results
        result_cols = [
            'symbol', 'currentPrice', 'dte', 'longPutStrike', 'shortPutStrike',
            'shortCallStrike', 'longCallStrike', 'netPremium', 'maxProfit', 'maxLoss',
            'lowerBreakeven', 'upperBreakeven', 'probabilityOfProfit', 'annualizedRoi',
            'winRateScore'
        ]

        final_results = best_candidates[result_cols].sort_values(by=sort_by, ascending=False)
        return final_results.head(max_candidates)

    def _estimate_put_delta(self, puts_df: pd.DataFrame) -> pd.Series:
        """Estimate put delta using Black-Scholes approximation."""
        deltas = []
        for _, row in puts_df.iterrows():
            try:
                S = row['currentPrice']  # Current stock price
                K = row['strike']        # Strike price
                T = row['dte'] / 365.0   # Time to expiration in years
                r = 0.05                 # Risk-free rate (5% assumption)

                # Estimate volatility from implied volatility if available
                if 'impliedVolatility' in row and pd.notna(row['impliedVolatility']):
                    sigma = row['impliedVolatility']
                else:
                    # Rough estimate based on option price
                    option_price = (row['bid'] + row['ask']) / 2
                    sigma = max(0.15, min(2.0, option_price / S * 4))  # Rough heuristic

                # Black-Scholes delta calculation for puts
                if T > 0 and sigma > 0:
                    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
                    delta = norm.cdf(d1) - 1  # Put delta is negative
                else:
                    delta = -0.5  # Default estimate

                deltas.append(delta)
            except Exception:
                deltas.append(-0.5)  # Default fallback

        return pd.Series(deltas, index=puts_df.index)

    def _estimate_call_delta(self, calls_df: pd.DataFrame) -> pd.Series:
        """Estimate call delta using Black-Scholes approximation."""
        deltas = []
        for _, row in calls_df.iterrows():
            try:
                S = row['currentPrice']  # Current stock price
                K = row['strike']        # Strike price
                T = row['dte'] / 365.0   # Time to expiration in years
                r = 0.05                 # Risk-free rate (5% assumption)

                # Estimate volatility from implied volatility if available
                if 'impliedVolatility' in row and pd.notna(row['impliedVolatility']):
                    sigma = row['impliedVolatility']
                else:
                    # Rough estimate based on option price
                    option_price = (row['bid'] + row['ask']) / 2
                    sigma = max(0.15, min(2.0, option_price / S * 4))  # Rough heuristic

                # Black-Scholes delta calculation for calls
                if T > 0 and sigma > 0:
                    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
                    delta = norm.cdf(d1)  # Call delta is positive
                else:
                    delta = 0.5  # Default estimate

                deltas.append(delta)
            except Exception:
                deltas.append(0.5)  # Default fallback

        return pd.Series(deltas, index=calls_df.index)

    def _calculate_iron_condor_probability(self, current_price: float, lower_breakeven: float,
                                         upper_breakeven: float, dte: int) -> float:
        """Calculate probability of profit for iron condor (simplified)."""
        try:
            # Simplified probability calculation based on normal distribution
            # In practice, this would use more sophisticated models
            range_width = upper_breakeven - lower_breakeven
            price_range = range_width / current_price

            # Rough estimate: wider range = higher probability
            # This is a simplified heuristic
            base_prob = min(0.8, price_range * 2)

            # Adjust for time decay (longer time = lower probability)
            time_factor = max(0.3, 1 - (dte / 365) * 0.5)

            return base_prob * time_factor
        except Exception:
            return 0.5  # Default fallback

    def _calculate_iron_condor_win_rate_score(self, df: pd.DataFrame) -> pd.Series:
        """Calculate composite win rate score for iron condors."""
        # 1. Probability of Profit Score (40% weight)
        prob_score = df['probabilityOfProfit'] * 100

        # 2. Return on Risk Score (30% weight)
        ror_score = (df['returnOnRisk'].clip(0, 50) / 50) * 100

        # 3. Time Score (15% weight) - 30-45 DTE is optimal
        optimal_dte = 37.5
        time_score = 100 - (abs(df['dte'] - optimal_dte) / optimal_dte * 100).clip(0, 100)

        # 4. Balance Score (15% weight) - prefer balanced wings
        wing_balance = abs(df['putWidth'] - df['callWidth']) / ((df['putWidth'] + df['callWidth']) / 2)
        balance_score = np.maximum(0, 100 - wing_balance * 100)

        # Weighted composite score
        win_rate_score = (
            prob_score * 0.40 +
            ror_score * 0.30 +
            time_score * 0.15 +
            balance_score * 0.15
        )

        return win_rate_score.round(1)

    def _categorize_dte(self, dte: int) -> str:
        """Categorize DTE for scoring purposes."""
        if dte < 22:
            return 'Short'
        elif dte <= 35:
            return 'Optimal'
        elif dte <= 50:
            return 'Extended'
        else:
            return 'Long'
