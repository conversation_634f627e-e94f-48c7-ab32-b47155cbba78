"""
Comprehensive test suite for options strategies analysis.

Tests all strategy analysis algorithms including:
- Cash-Secured Puts analysis
- Covered Calls analysis
- Iron Condors analysis
- Win Rate Score calculations
- Edge cases and error handling
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.services.options_strategies import OptionsStrategiesAnalyzer


@pytest.mark.unit
@pytest.mark.options
@pytest.mark.strategies
class TestOptionsStrategiesAnalyzer:
    """Test the OptionsStrategiesAnalyzer functionality."""
    
    @pytest.fixture
    def analyzer(self):
        """Create an OptionsStrategiesAnalyzer instance for testing."""
        return OptionsStrategiesAnalyzer()
    
    @pytest.fixture
    def comprehensive_options_data(self):
        """Create comprehensive options data for testing all strategies."""
        base_date = datetime.now() + timedelta(days=30)
        expiration = base_date.strftime('%Y-%m-%d')
        
        data = []
        
        # AAPL options (current price: 150.0)
        aapl_strikes_puts = [130, 135, 140, 145, 148, 150]
        aapl_strikes_calls = [150, 152, 155, 160, 165, 170, 175, 180]
        
        for strike in aapl_strikes_puts:
            data.append({
                'symbol': 'AAPL',
                'optionType': 'puts',
                'strike': strike,
                'expiration': expiration,
                'dte': 30,
                'bid': max(0.1, (150 - strike) * 0.6 + np.random.uniform(0.5, 2.0)),
                'ask': max(0.2, (150 - strike) * 0.6 + np.random.uniform(1.0, 2.5)),
                'impliedVolatility': 0.20 + np.random.uniform(0.05, 0.15),
                'volume': np.random.randint(50, 500),
                'openInterest': np.random.randint(100, 1000)
            })
        
        for strike in aapl_strikes_calls:
            data.append({
                'symbol': 'AAPL',
                'optionType': 'calls',
                'strike': strike,
                'expiration': expiration,
                'dte': 30,
                'bid': max(0.1, max(0, 150 - strike) + np.random.uniform(0.5, 3.0)),
                'ask': max(0.2, max(0, 150 - strike) + np.random.uniform(1.0, 3.5)),
                'impliedVolatility': 0.18 + np.random.uniform(0.05, 0.12),
                'volume': np.random.randint(30, 400),
                'openInterest': np.random.randint(80, 800)
            })
        
        return pd.DataFrame(data)
    
    @pytest.fixture
    def sample_prices(self):
        """Create sample current stock prices."""
        return {
            'AAPL': 150.0,
            'MSFT': 300.0,
            'GOOGL': 2500.0
        }
    
    @pytest.fixture
    def default_put_params(self):
        """Default parameters for cash-secured puts testing."""
        return {
            'min_dte': 20,
            'max_dte': 60,
            'min_annual_roi': 0.10,
            'min_buffer_percent': 0.02,
            'max_delta': 0.35,
            'sort_by': 'winRateScore',
            'max_candidates': 10
        }
    
    @pytest.fixture
    def default_call_params(self):
        """Default parameters for covered calls testing."""
        return {
            'min_dte': 20,
            'max_dte': 60,
            'min_annual_roi': 0.08,
            'min_upside_buffer': 0.01,
            'max_delta': 0.35,
            'sort_by': 'annualizedRoi',
            'max_candidates': 10
        }

    def test_cash_secured_puts_basic_analysis(self, analyzer, comprehensive_options_data, sample_prices, default_put_params):
        """Test basic cash-secured puts analysis functionality."""
        candidates = analyzer.find_cash_secured_put_candidates(
            comprehensive_options_data, sample_prices, default_put_params
        )
        
        # Should find some candidates
        assert not candidates.empty
        
        # All candidates should be puts
        assert all(candidates['optionType'] == 'puts')
        
        # All strikes should be below current price (OTM puts)
        assert all(candidates['strike'] < sample_prices['AAPL'])
        
        # DTE should be within specified range
        assert all(candidates['dte'] >= default_put_params['min_dte'])
        assert all(candidates['dte'] <= default_put_params['max_dte'])
        
        # Required columns should exist
        required_columns = [
            'symbol', 'currentPrice', 'strike', 'expiration', 'dte', 'bid', 'premium',
            'maxGain', 'maxLoss', 'annualizedRoi', 'bufferPercent', 'winRateScore'
        ]
        for col in required_columns:
            assert col in candidates.columns
        
        # Validate calculated fields
        assert all(candidates['premium'] == candidates['bid'] * 100)
        assert all(candidates['maxGain'] == candidates['premium'])
        assert all(candidates['bufferPercent'] >= default_put_params['min_buffer_percent'])
        assert all(candidates['annualizedRoi'] >= default_put_params['min_annual_roi'])
    
    def test_cash_secured_puts_empty_data(self, analyzer, sample_prices, default_put_params):
        """Test cash-secured puts analysis with empty data."""
        empty_data = pd.DataFrame()
        
        candidates = analyzer.find_cash_secured_put_candidates(
            empty_data, sample_prices, default_put_params
        )
        
        assert candidates.empty
    
    def test_cash_secured_puts_no_puts(self, analyzer, sample_prices, default_put_params):
        """Test cash-secured puts analysis with no put options."""
        # Create data with only calls
        calls_only_data = pd.DataFrame({
            'symbol': ['AAPL', 'AAPL'],
            'optionType': ['calls', 'calls'],
            'strike': [155.0, 160.0],
            'expiration': ['2024-02-16', '2024-02-16'],
            'dte': [30, 30],
            'bid': [2.0, 1.0],
            'ask': [2.5, 1.5],
            'impliedVolatility': [0.25, 0.23],
            'volume': [100, 50],
            'openInterest': [500, 300]
        })
        
        candidates = analyzer.find_cash_secured_put_candidates(
            calls_only_data, sample_prices, default_put_params
        )
        
        assert candidates.empty
    
    def test_covered_calls_basic_analysis(self, analyzer, comprehensive_options_data, sample_prices, default_call_params):
        """Test basic covered calls analysis functionality."""
        candidates = analyzer.find_covered_call_candidates(
            comprehensive_options_data, sample_prices, default_call_params
        )
        
        # Should find some candidates
        assert not candidates.empty
        
        # All candidates should be calls
        assert all(candidates['optionType'] == 'calls')
        
        # All strikes should be at or above current price (ATM/OTM calls)
        assert all(candidates['strike'] >= sample_prices['AAPL'])
        
        # DTE should be within specified range
        assert all(candidates['dte'] >= default_call_params['min_dte'])
        assert all(candidates['dte'] <= default_call_params['max_dte'])
        
        # Required columns should exist
        required_columns = [
            'symbol', 'currentPrice', 'strike', 'expiration', 'dte', 'bid', 'premium',
            'maxGain', 'annualizedRoi', 'upsideBufferPercent', 'winRateScore'
        ]
        for col in required_columns:
            assert col in candidates.columns
        
        # Validate calculated fields
        assert all(candidates['premium'] == candidates['bid'] * 100)
        assert all(candidates['maxGain'] == candidates['premium'])
        assert all(candidates['upsideBufferPercent'] >= default_call_params['min_upside_buffer'])
        assert all(candidates['annualizedRoi'] >= default_call_params['min_annual_roi'])
    
    def test_win_rate_score_calculation_puts(self, analyzer):
        """Test win rate score calculation for puts with known data."""
        # Create test data with known values
        test_data = pd.DataFrame({
            'bufferPercent': [0.05, 0.10, 0.15],
            'dte': [30, 45, 60],
            'impliedVolatility': [0.20, 0.30, 0.40],
            'bid': [2.0, 3.0, 4.0],
            'ask': [2.2, 3.3, 4.4]
        })
        
        scores = analyzer._calculate_put_win_rate_score(test_data)
        
        # Should return a Series with same length
        assert len(scores) == len(test_data)
        assert isinstance(scores, pd.Series)
        
        # All scores should be between 0 and 100
        assert all(scores >= 0)
        assert all(scores <= 100)
        
        # Higher buffer should generally give higher score
        # (though this might not always be true due to other factors)
        assert isinstance(scores.iloc[0], (int, float))
    
    def test_win_rate_score_calculation_calls(self, analyzer):
        """Test win rate score calculation for calls with known data."""
        test_data = pd.DataFrame({
            'upsideBufferPercent': [0.02, 0.05, 0.10],
            'dte': [30, 45, 60],
            'impliedVolatility': [0.20, 0.30, 0.40],
            'bid': [2.0, 3.0, 4.0],
            'ask': [2.2, 3.3, 4.4]
        })
        
        scores = analyzer._calculate_call_win_rate_score(test_data)
        
        # Should return a Series with same length
        assert len(scores) == len(test_data)
        assert isinstance(scores, pd.Series)
        
        # All scores should be between 0 and 100
        assert all(scores >= 0)
        assert all(scores <= 100)
    
    def test_dte_categorization(self, analyzer):
        """Test DTE categorization logic with edge cases."""
        test_cases = [
            (1, 'Short'),
            (21, 'Short'),
            (22, 'Optimal'),
            (35, 'Optimal'),
            (36, 'Extended'),
            (50, 'Extended'),
            (51, 'Long'),
            (100, 'Long'),
            (365, 'Long')
        ]
        
        for dte, expected_category in test_cases:
            category = analyzer._categorize_dte(dte)
            assert category == expected_category, f"DTE {dte} should be {expected_category}, got {category}"
    
    def test_delta_estimation_puts(self, analyzer):
        """Test put delta estimation with realistic data."""
        test_data = pd.DataFrame({
            'currentPrice': [150.0, 150.0, 150.0],
            'strike': [140.0, 145.0, 150.0],
            'dte': [30, 30, 30],
            'impliedVolatility': [0.25, 0.25, 0.25]
        })
        
        deltas = analyzer._estimate_put_delta(test_data)
        
        # Should return negative deltas for puts
        assert all(deltas <= 0)
        
        # OTM puts should have deltas closer to 0, ATM puts closer to -0.5
        assert deltas.iloc[0] > deltas.iloc[1]  # 140 strike > 145 strike
        assert deltas.iloc[1] > deltas.iloc[2]  # 145 strike > 150 strike
    
    def test_delta_estimation_calls(self, analyzer):
        """Test call delta estimation with realistic data."""
        test_data = pd.DataFrame({
            'currentPrice': [150.0, 150.0, 150.0],
            'strike': [150.0, 155.0, 160.0],
            'dte': [30, 30, 30],
            'impliedVolatility': [0.25, 0.25, 0.25]
        })
        
        deltas = analyzer._estimate_call_delta(test_data)
        
        # Should return positive deltas for calls
        assert all(deltas >= 0)
        
        # ATM calls should have higher deltas than OTM calls
        assert deltas.iloc[0] > deltas.iloc[1]  # 150 strike > 155 strike
        assert deltas.iloc[1] > deltas.iloc[2]  # 155 strike > 160 strike
