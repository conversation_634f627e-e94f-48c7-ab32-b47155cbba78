[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Default options
addopts =
    --verbose
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=90
    --tb=short
    --strict-markers
    --strict-config
    --durations=10
    -ra

# Test markers for categorization
markers =
    unit: Unit tests for individual functions/methods
    integration: Integration tests across multiple components
    slow: Slow running tests (>1 second)
    api: API endpoint tests
    database: Database related tests
    external: Tests that require external services (should be mocked)
    options: Options analysis specific tests
    strategies: Strategy analysis tests
    data: Data fetching and management tests
    watchlist: Watchlist management tests
    config: Configuration management tests

# Warning filters
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:pkg_resources is deprecated:UserWarning
    ignore::UserWarning:yahooquery
    ignore::FutureWarning:pandas

# Minimum pytest version
minversion = 6.0

# Test timeout (5 minutes for comprehensive tests)
timeout = 300