import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Settings as SettingsIcon,
  PlayArrow as PlayArrowIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ExpandMore as ExpandMoreIcon,
  Assessment as AssessmentIcon,
  Security as SecurityIcon,
  AccountBalance as AccountBalanceIcon,
} from '@mui/icons-material';
import { toast } from 'react-hot-toast';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';
import WatchlistDialog from './WatchlistDialog';
import StrategyConfigDialog from './StrategyConfigDialog';

const OptionsAnalysisPanel = ({ selectedAccount }) => {
  // State management
  const [selectedStrategy, setSelectedStrategy] = useState('wheel');
  const [watchlists, setWatchlists] = useState([]);
  const [selectedWatchlist, setSelectedWatchlist] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [marketConditions, setMarketConditions] = useState(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [watchlistDialogOpen, setWatchlistDialogOpen] = useState(false);
  const [strategyConfig, setStrategyConfig] = useState({});
  const [tabValue, setTabValue] = useState(0);

  // Strategy options
  const strategyOptions = [
    { value: 'wheel', label: 'Wheel Strategy (轮动策略)', icon: <TrendingUpIcon /> },
    { value: 'cash_secured_puts', label: 'Cash-Secured Puts (现金担保看跌)', icon: <TrendingDownIcon /> },
    { value: 'covered_calls', label: 'Covered Calls (备兑看涨)', icon: <TrendingUpIcon /> },
    { value: 'iron_condors', label: 'Iron Condors (铁鹰策略)', icon: <AssessmentIcon /> },
  ];

  // Load watchlists and default config on mount
  useEffect(() => {
    if (selectedAccount) {
      loadWatchlists();
      loadStrategyConfig();
    }
  }, [selectedAccount, selectedStrategy]);

  const loadWatchlists = async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_WATCHLISTS}?account_id=${selectedAccount}`
      );
      const data = await response.json();
      
      if (response.ok) {
        setWatchlists(data.watchlists || []);
        if (data.watchlists && data.watchlists.length > 0) {
          setSelectedWatchlist(data.watchlists[0]);
        }
      } else {
        toast.error(`加载观察列表失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`加载观察列表失败: ${error.message}`);
    }
  };

  const loadStrategyConfig = async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_CONFIG}/${selectedStrategy}?account_id=${selectedAccount}`
      );
      const data = await response.json();
      
      if (response.ok) {
        setStrategyConfig(data.config || {});
      } else {
        toast.error(`加载策略配置失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`加载策略配置失败: ${error.message}`);
    }
  };

  const runAnalysis = async () => {
    if (!selectedWatchlist || !selectedWatchlist.symbols || selectedWatchlist.symbols.length === 0) {
      toast.error('请先选择包含股票的观察列表');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisResults(null);

    try {
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.OPTIONS_ANALYZE}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          account_id: selectedAccount,
          strategy_type: selectedStrategy,
          symbols: selectedWatchlist.symbols,
          config: strategyConfig,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setAnalysisResults(data);
        setMarketConditions(data.market_conditions);
        toast.success('期权分析完成！');
      } else {
        toast.error(`分析失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`分析失败: ${error.message}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderStrategySelector = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <SecurityIcon sx={{ mr: 1 }} />
          策略选择
        </Typography>
        <FormControl fullWidth>
          <InputLabel>选择期权策略</InputLabel>
          <Select
            value={selectedStrategy}
            onChange={(e) => setSelectedStrategy(e.target.value)}
            label="选择期权策略"
          >
            {strategyOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {option.icon}
                  <Typography sx={{ ml: 1 }}>{option.label}</Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </CardContent>
    </Card>
  );

  const renderWatchlistSelector = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <AccountBalanceIcon sx={{ mr: 1 }} />
            观察列表
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={() => setWatchlistDialogOpen(true)}
            size="small"
          >
            管理列表
          </Button>
        </Box>
        
        {watchlists.length > 0 ? (
          <FormControl fullWidth>
            <InputLabel>选择观察列表</InputLabel>
            <Select
              value={selectedWatchlist?.watchlist_id || ''}
              onChange={(e) => {
                const watchlist = watchlists.find(w => w.watchlist_id === e.target.value);
                setSelectedWatchlist(watchlist);
              }}
              label="选择观察列表"
            >
              {watchlists.map((watchlist) => (
                <MenuItem key={watchlist.watchlist_id} value={watchlist.watchlist_id}>
                  <Box>
                    <Typography variant="body1">{watchlist.name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {watchlist.symbols.length} 只股票
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        ) : (
          <Alert severity="info">
            暂无观察列表，请先创建一个观察列表
          </Alert>
        )}
        
        {selectedWatchlist && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              当前列表股票:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {selectedWatchlist.symbols.map((symbol) => (
                <Chip key={symbol} label={symbol} size="small" />
              ))}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const renderAnalysisControls = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">分析控制</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<SettingsIcon />}
              onClick={() => setConfigDialogOpen(true)}
              variant="outlined"
              size="small"
            >
              配置参数
            </Button>
            <Button
              startIcon={isAnalyzing ? <CircularProgress size={16} /> : <PlayArrowIcon />}
              onClick={runAnalysis}
              variant="contained"
              disabled={isAnalyzing || !selectedWatchlist}
            >
              {isAnalyzing ? '分析中...' : '开始分析'}
            </Button>
          </Box>
        </Box>
        
        {marketConditions && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              市场状况:
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Chip 
                label={`波动率: ${marketConditions.volatility_regime}`}
                color={marketConditions.volatility_regime === 'High Volatility' ? 'error' : 
                       marketConditions.volatility_regime === 'Low Volatility' ? 'success' : 'default'}
                size="small"
              />
              <Chip 
                label={`压力指数: ${marketConditions.stress_indicator?.toFixed(1) || 'N/A'}`}
                color={marketConditions.stress_indicator > 75 ? 'error' : 
                       marketConditions.stress_indicator < 25 ? 'success' : 'warning'}
                size="small"
              />
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );

  const renderAnalysisResults = () => {
    if (!analysisResults) return null;

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            分析结果
          </Typography>
          
          {selectedStrategy === 'wheel' ? (
            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
              <Tab label={`现金担保看跌 (${analysisResults.put_candidates?.length || 0})`} />
              <Tab label={`备兑看涨 (${analysisResults.call_candidates?.length || 0})`} />
            </Tabs>
          ) : null}
          
          <Box sx={{ mt: 2 }}>
            {selectedStrategy === 'wheel' ? (
              <>
                {tabValue === 0 && renderCandidatesTable(analysisResults.put_candidates, 'puts')}
                {tabValue === 1 && renderCandidatesTable(analysisResults.call_candidates, 'calls')}
              </>
            ) : (
              renderCandidatesTable(analysisResults.candidates, selectedStrategy)
            )}
          </Box>
        </CardContent>
      </Card>
    );
  };

  const renderCandidatesTable = (candidates, type) => {
    if (!candidates || candidates.length === 0) {
      return (
        <Alert severity="info">
          没有找到符合条件的候选项
        </Alert>
      );
    }

    // Define columns based on strategy type
    const getColumns = () => {
      const baseColumns = ['symbol', 'currentPrice', 'strike', 'expiration', 'dte'];
      
      if (type === 'puts' || selectedStrategy === 'cash_secured_puts') {
        return [...baseColumns, 'premium', 'annualizedRoi', 'bufferPercent', 'winRateScore'];
      } else if (type === 'calls' || selectedStrategy === 'covered_calls') {
        return [...baseColumns, 'premium', 'annualizedRoi', 'upsideBufferPercent', 'winRateScore'];
      } else if (selectedStrategy === 'iron_condors') {
        return ['symbol', 'currentPrice', 'dte', 'netPremium', 'maxProfit', 'maxLoss', 
                'probabilityOfProfit', 'annualizedRoi', 'winRateScore'];
      }
      return baseColumns;
    };

    const columns = getColumns();

    return (
      <TableContainer component={Paper} variant="outlined">
        <Table size="small">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column}>
                  {getColumnLabel(column)}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {candidates.map((candidate, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell key={column}>
                    {formatCellValue(candidate[column], column)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  const getColumnLabel = (column) => {
    const labels = {
      symbol: '股票',
      currentPrice: '当前价格',
      strike: '行权价',
      expiration: '到期日',
      dte: '剩余天数',
      premium: '权利金',
      annualizedRoi: '年化收益率',
      bufferPercent: '缓冲比例',
      upsideBufferPercent: '上涨缓冲',
      winRateScore: '胜率评分',
      netPremium: '净权利金',
      maxProfit: '最大收益',
      maxLoss: '最大损失',
      probabilityOfProfit: '盈利概率',
    };
    return labels[column] || column;
  };

  const formatCellValue = (value, column) => {
    if (value === null || value === undefined) return 'N/A';
    
    if (['currentPrice', 'strike', 'premium', 'netPremium', 'maxProfit', 'maxLoss'].includes(column)) {
      return `$${Number(value).toFixed(2)}`;
    }
    
    if (['annualizedRoi', 'bufferPercent', 'upsideBufferPercent', 'probabilityOfProfit'].includes(column)) {
      return `${(Number(value) * 100).toFixed(2)}%`;
    }
    
    if (column === 'winRateScore') {
      return Number(value).toFixed(1);
    }
    
    if (column === 'expiration') {
      return new Date(value).toLocaleDateString();
    }
    
    return value;
  };

  return (
    <Box>
      {renderStrategySelector()}
      {renderWatchlistSelector()}
      {renderAnalysisControls()}
      {renderAnalysisResults()}

      {/* Configuration Dialog */}
      <StrategyConfigDialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        selectedAccount={selectedAccount}
        strategyType={selectedStrategy}
        currentConfig={strategyConfig}
        onConfigSaved={(newConfig) => {
          setStrategyConfig(newConfig);
          toast.success('配置已更新');
        }}
      />

      {/* Watchlist Management Dialog */}
      <WatchlistDialog
        open={watchlistDialogOpen}
        onClose={() => setWatchlistDialogOpen(false)}
        selectedAccount={selectedAccount}
        onWatchlistsUpdated={loadWatchlists}
      />
    </Box>
  );
};

export default OptionsAnalysisPanel;
